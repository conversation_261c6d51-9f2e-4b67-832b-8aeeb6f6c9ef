<template>
  <div class="globe-container" id="container" ref="globeContainer"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import earthFlyLine from "earth-flyline";
import geojson from "../common/world.json";

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
});

const globeContainer = ref(null);
let earthInstance = null;

// 地球配置
const earthConfig = {
  bgStyle: {
    color: "#040D21",
    opacity: 0,
  },
  mapStyle: {
    areaColor: "#013e87",
    lineColor: "#516aaf",
  },
  spriteStyle: {
    color: "#138cdf",
    size: 2.5,
    show: true,
  },
  pathStyle: {
    color: "#7aaae9",
    show: true,
  },
  flyLineStyle: {
    color: "#02fff6",
    show: true,
  },
  roadStyle: {
    flyLineStyle: {
      color: "#02fff6",
    },
    pathStyle: {
      color: "#02fff6",
    },
  },
  scatterStyle: {
    color: "#02fff6",
  },
  wallStyle: {
    color: "#02fff6",
    opacity: 0.5,
  },
  mapStreamStyle: {
    color: "#f0f0f0",
    opacity: 0.5,
    speed: 1,
    splitLine: 3,
  },
};

onMounted(() => {
  initEarth();
  window.addEventListener("resize", handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
  if (earthInstance) {
    earthInstance.dispose();
  }
});

function initEarth() {
  earthFlyLine.registerMap("world", geojson);

  const dom = document.getElementById("container");
  const chart = earthFlyLine.init({
    dom,
    map: "world",
    config: earthConfig,
    rotateSpeed: 0.005,
  });

  const chinaData = geojson.features.find((item) => {
    return item.properties.name === "China";
  }).geometry.coordinates;
  chinaData.forEach((item) => {
    chart.addData("mapStreamLine", {
      data: item, //请注意 data 的数据结构: [[[lon,lat],[lon,lat],[lon,lat]]] 三维数组，第一维代表多边形，第二维代表多边形的点位，第三维代表点位的经纬度
      style: {
        //...
        color: "#f0f0f0",
        opacity: 0.5,
        speed: 1,
        splitLine: 3,
      },
    });
  });
  const flyLineData = [
    {
      from: {
        id: 1,
        lon: 112.45, //经度
        lat: 34.62, //维度
      },
      to: {
        id: 2,
        lon: 14, //经度
        lat: 52, //维度
      },
    },
  ];
  chart.addData("flyLine", flyLineData);
}

function updateEarthData() {
  if (!earthInstance || !props.data || props.data.length === 0) return;

  // 清除现有数据
  earthInstance.clear();

  // 准备点数据
  const points = [];
  const uniqueLocations = new Set();

  // 收集所有唯一的地点
  props.data.forEach((item) => {
    const fromKey = `${item.from[0]},${item.from[1]}`;
    const toKey = `${item.to[0]},${item.to[1]}`;

    if (!uniqueLocations.has(fromKey)) {
      uniqueLocations.add(fromKey);
      points.push({
        name: item.fromName || "起点",
        coordinate: item.from,
        value: item.value || 100,
      });
    }

    if (!uniqueLocations.has(toKey)) {
      uniqueLocations.add(toKey);
      points.push({
        name: item.toName || "终点",
        coordinate: item.to,
        value: item.value || 100,
      });
    }
  });

  // 添加散点
  earthInstance.addScatter(points);

  // 添加飞线
  const flylines = props.data.map((item) => ({
    source: item.from,
    target: item.to,
    value: item.value || 100,
    name: item.name || `${item.fromName || "起点"} - ${item.toName || "终点"}`,
  }));

  earthInstance.addFlyLine(flylines);

  // 渲染地球
  earthInstance.render();
}

function handleResize() {
  if (earthInstance && globeContainer.value) {
    earthInstance.resize();
  }
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    updateEarthData();
  },
  { deep: true }
);
</script>

<style scoped>
.globe-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: rgba(4, 13, 33, 0.3);
  border-radius: 8px;
}
</style>
