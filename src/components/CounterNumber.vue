<template>
  <div class="counter-number">
    <div class="title">{{ title }}</div>
    <div class="value">{{ displayValue }}{{ unit }}</div>
    <div class="energy-bar">
      <div
        v-for="(cell, index) in totalCells"
        :key="index"
        class="energy-cell"
        :class="{ active: index < activeCells }"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";

const props = defineProps({
  value: {
    type: Number,
    default: 0,
  },
  unit: {
    type: String,
    default: "",
  },
  title: {
    type: String,
    default: "",
  },
});

const displayValue = ref(0);
const duration = 2000; // 动画持续时间（毫秒）
const interval = 20; // 更新间隔（毫秒）
const totalCells = 20; // 能量格子总数
const activeCells = ref(0); // 当前激活的格子数

function animateValue(start, end) {
  let startTime = null;

  function step(timestamp) {
    if (!startTime) startTime = timestamp;
    const progress = Math.min((timestamp - startTime) / duration, 1);
    displayValue.value = Math.floor(progress * (end - start) + start);

    // 更新能量格子
    const cellProgress = Math.min(progress * 1.2, 1); // 稍微超过1以确保最后一格也能填满
    activeCells.value = Math.floor(cellProgress * totalCells);

    if (progress < 1) {
      window.requestAnimationFrame(step);
    }
  }

  window.requestAnimationFrame(step);
}

onMounted(() => {
  animateValue(0, props.value);
});

watch(
  () => props.value,
  (newValue, oldValue) => {
    // 重置能量格子
    activeCells.value = 0;
    // 开始新的动画
    animateValue(oldValue, newValue);
  }
);
</script>

<style scoped>
.counter-number {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.title {
  font-size: 14px;
  color: #00e4ff;
  margin-bottom: 5px;
}

.value {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 0 10px rgba(0, 228, 255, 0.8);
  margin-bottom: 10px;
}

.energy-bar {
  display: flex;
  width: 100%;
  height: 8px;
  background-color: rgba(0, 20, 40, 0.5);
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 0 5px rgba(0, 228, 255, 0.3) inset;
}

.energy-cell {
  flex: 1;
  height: 100%;
  margin: 0 1px;
  background-color: rgba(0, 50, 80, 0.5);
  transition: all 0.2s ease;
}

.energy-cell.active {
  background-color: #00e4ff;
  box-shadow: 0 0 8px #00e4ff;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}
</style>
